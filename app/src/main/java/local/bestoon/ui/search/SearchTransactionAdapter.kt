package local.bestoon.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.SearchTransaction
import java.text.NumberFormat
import java.util.Locale

/**
 * Adapter for displaying search transaction results in a RecyclerView.
 */
class SearchTransactionAdapter : ListAdapter<SearchTransaction, SearchTransactionAdapter.SearchTransactionViewHolder>(SearchTransactionDiffCallback()) {

    class SearchTransactionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchTransactionViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_search_transaction, parent, false)
        return SearchTransactionViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchTransactionViewHolder, position: Int) {
        val transaction = getItem(position)
        
        holder.textShortUuid.text = transaction.shortUuid
        holder.textTitle.text = transaction.title ?: "بدون عنوان"
        
        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(transaction.amount)} تومان"
        
        // Set color based on transaction mode (I = Income, E = Expenditure)
        val amountColor = if (transaction.mode == "I") {
            holder.itemView.context.getColor(android.R.color.holo_green_dark)
        } else {
            holder.itemView.context.getColor(android.R.color.holo_red_dark)
        }
        holder.textAmount.setTextColor(amountColor)
        
        holder.textBank.text = transaction.bankInfo?.title ?: "بدون بانک"
        holder.textCategory.text = transaction.categoryInfo?.title ?: "بدون دسته‌بندی"
        
        // Display tags
        if (transaction.tagsNames.isNotEmpty()) {
            holder.textTags.text = transaction.tagsNames.joinToString(", ")
            holder.textTags.visibility = View.VISIBLE
        } else {
            holder.textTags.visibility = View.GONE
        }
        
        holder.textCreated.text = transaction.slashedDatePersian
    }

    class SearchTransactionDiffCallback : DiffUtil.ItemCallback<SearchTransaction>() {
        override fun areItemsTheSame(oldItem: SearchTransaction, newItem: SearchTransaction): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: SearchTransaction, newItem: SearchTransaction): Boolean {
            return oldItem == newItem
        }
    }
}
