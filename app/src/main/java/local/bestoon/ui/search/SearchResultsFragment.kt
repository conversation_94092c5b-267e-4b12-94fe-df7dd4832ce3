package local.bestoon.ui.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayoutMediator
import local.bestoon.R
import local.bestoon.data.model.SearchTransaction
import local.bestoon.data.model.SearchEvent
import local.bestoon.databinding.FragmentSearchResultsBinding
import local.bestoon.data.SessionManager

/**
 * Fragment for displaying search results.
 */
class SearchResultsFragment : Fragment() {

    private var _binding: FragmentSearchResultsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: SearchResultsViewModel
    private lateinit var sessionManager: SessionManager
    private lateinit var pagerAdapter: SearchPagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSearchResultsBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(this)[SearchResultsViewModel::class.java]
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupViewPager()
        observeViewModel()

        // Get the search query from arguments
        arguments?.getString("query")?.let { query ->
            performSearch(query)
        }
    }

    /**
     * Sets up the ViewPager with tabs for displaying search results.
     */
    private fun setupViewPager() {
        // Setup ViewPager with tabs
        pagerAdapter = SearchPagerAdapter()
        binding.viewPager.adapter = pagerAdapter

        // Setup TabLayout with ViewPager
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "تراکنش"
                1 -> "رویداد"
                else -> ""
            }
        }.attach()
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.searchQuery.observe(viewLifecycleOwner) { query ->
            binding.tvSearchQuery.text = getString(R.string.search_query, query)
        }

        viewModel.transactions.observe(viewLifecycleOwner) { transactions ->
            updateViewPagerData(transactions, viewModel.events.value ?: emptyList())
        }

        viewModel.events.observe(viewLifecycleOwner) { events ->
            updateViewPagerData(viewModel.transactions.value ?: emptyList(), events)
        }


        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Updates the ViewPager data with new transactions and events.
     */
    private fun updateViewPagerData(transactions: List<SearchTransaction>, events: List<SearchEvent>) {
        pagerAdapter.updateData(transactions, events)
    }

    /**
     * Performs a search with the given query.
     *
     * @param query The search query string.
     */
    private fun performSearch(query: String) {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.search(token, query)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
        }
    }



    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
