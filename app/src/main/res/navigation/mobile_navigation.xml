<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="local.bestoon.ui.home.HomeFragment"
        android:label="@string/menu_home"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_nav_home_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_nav_home_to_editTransactionFragment"
            app:destination="@id/editTransactionFragment" />
        <action
            android:id="@+id/action_nav_home_to_editEventFragment"
            app:destination="@id/editEventFragment" />
    </fragment>

    <fragment
        android:id="@+id/nav_tickets"
        android:name="local.bestoon.ui.tickets.TicketsFragment"
        android:label="@string/menu_tickets"
        tools:layout="@layout/fragment_tickets">
        <argument
            android:name="status"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null" />
        <argument
            android:name="groupId"
            app:argType="integer"
            android:defaultValue="-1" />
        <action
            android:id="@+id/action_nav_tickets_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_nav_tickets_to_nav_new_ticket"
            app:destination="@id/nav_new_ticket" />
    </fragment>

    <fragment
        android:id="@+id/ticketDetailsFragment"
        android:name="local.bestoon.ui.ticketdetails.TicketDetailsFragment"
        android:label="@string/ticket_details"
        tools:layout="@layout/fragment_ticket_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_ticketDetailsFragment_to_nav_tickets"
            app:destination="@id/nav_tickets"
            app:popUpTo="@id/nav_tickets"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_ticket"
        android:name="local.bestoon.ui.newticket.NewTicketFragment"
        android:label="@string/new_ticket"
        tools:layout="@layout/fragment_new_ticket">
        <action
            android:id="@+id/action_nav_new_ticket_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_profile"
        android:name="local.bestoon.ui.profile.ProfileFragment"
        android:label="@string/menu_profile"
        tools:layout="@layout/fragment_profile" />







    <fragment
        android:id="@+id/nav_users"
        android:name="local.bestoon.ui.users.UsersFragment"
        android:label="@string/menu_users"
        tools:layout="@layout/fragment_users" />

    <fragment
        android:id="@+id/nav_new_transaction"
        android:name="local.bestoon.ui.newtransaction.NewTransactionFragment"
        android:label="@string/new_transaction"
        tools:layout="@layout/fragment_new_transaction" />

    <fragment
        android:id="@+id/nav_new_event"
        android:name="local.bestoon.ui.newevent.NewEventFragment"
        android:label="@string/new_event"
        tools:layout="@layout/fragment_new_event">
        <action
            android:id="@+id/action_newEventFragment_to_newEventFragment"
            app:destination="@id/nav_new_event"
            app:popUpTo="@id/nav_new_event"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/editTransactionFragment"
        android:name="local.bestoon.ui.edittransaction.EditTransactionFragment"
        android:label="@string/edit_transaction"
        tools:layout="@layout/fragment_edit_transaction">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="incomeObject"
            app:argType="local.bestoon.data.model.IncomeObject"
            app:nullable="true" />
        <argument
            android:name="expenditureObject"
            app:argType="local.bestoon.data.model.ExpenditureObject"
            app:nullable="true" />
        <action
            android:id="@+id/action_editTransactionFragment_to_nav_home"
            app:destination="@id/nav_home"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/editEventFragment"
        android:name="local.bestoon.ui.editevent.EditEventFragment"
        android:label="@string/edit_event"
        tools:layout="@layout/fragment_edit_event">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="eventObject"
            app:argType="local.bestoon.data.model.EventObject"
            app:nullable="false" />
        <action
            android:id="@+id/action_editEventFragment_to_nav_home"
            app:destination="@id/nav_home"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/searchResultsFragment"
        android:name="local.bestoon.ui.search.SearchResultsFragment"
        android:label="@string/search_results"
        tools:layout="@layout/fragment_search_results">
        <argument
            android:name="query"
            app:argType="string" />
        <action
            android:id="@+id/action_searchResultsFragment_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />

    </fragment>
</navigation>
