<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="2dp"
    android:layout_marginEnd="2dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- First row: created, author, short_uuid -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/text_created"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تاریخ ایجاد"
                android:textSize="11sp"
                android:textColor="?android:attr/textColorTertiary" />

            <TextView
                android:id="@+id/text_author"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="نویسنده"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/text_short_uuid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="UUID"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary" />

        </LinearLayout>

        <!-- Second row: title -->
        <TextView
            android:id="@+id/text_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="عنوان رویداد"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
