package local.bestoon.ui.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayoutMediator
import android.widget.Toast
import android.os.Bundle
import local.bestoon.R
import local.bestoon.data.model.SearchTransaction
import local.bestoon.data.model.SearchEvent
import local.bestoon.data.model.IncomeObject
import local.bestoon.data.model.ExpenditureObject
import local.bestoon.data.model.EventObject
import local.bestoon.data.model.Author
import local.bestoon.data.model.Bank
import local.bestoon.data.model.HomepageCategory
import local.bestoon.databinding.FragmentSearchResultsBinding
import local.bestoon.data.SessionManager

/**
 * Fragment for displaying search results.
 */
class SearchResultsFragment : Fragment() {

    private var _binding: FragmentSearchResultsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: SearchResultsViewModel
    private lateinit var sessionManager: SessionManager
    private lateinit var pagerAdapter: SearchPagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSearchResultsBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(this)[SearchResultsViewModel::class.java]
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupViewPager()
        observeViewModel()

        // Get the search query from arguments
        arguments?.getString("query")?.let { query ->
            performSearch(query)
        }
    }

    /**
     * Sets up the ViewPager with tabs for displaying search results.
     */
    private fun setupViewPager() {
        // Setup ViewPager with tabs
        pagerAdapter = SearchPagerAdapter(
            onDeleteTransaction = { shortUuid ->
                deleteTransaction(shortUuid)
            },
            onEditTransaction = { shortUuid, transaction ->
                navigateToEditTransaction(shortUuid, transaction)
            },
            onDeleteEvent = { shortUuid ->
                deleteEvent(shortUuid)
            },
            onEditEvent = { shortUuid, event ->
                navigateToEditEvent(shortUuid, event)
            }
        )
        binding.viewPager.adapter = pagerAdapter

        // Setup TabLayout with ViewPager
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "تراکنش"
                1 -> "رویداد"
                else -> ""
            }
        }.attach()
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.searchQuery.observe(viewLifecycleOwner) { query ->
            binding.tvSearchQuery.text = getString(R.string.search_query, query)
        }

        viewModel.transactions.observe(viewLifecycleOwner) { transactions ->
            updateViewPagerData(transactions, viewModel.events.value ?: emptyList())
        }

        viewModel.events.observe(viewLifecycleOwner) { events ->
            updateViewPagerData(viewModel.transactions.value ?: emptyList(), events)
        }


        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }

        viewModel.successMessage.observe(viewLifecycleOwner) { successMessage ->
            if (successMessage.isNotEmpty()) {
                Toast.makeText(requireContext(), successMessage, Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * Updates the ViewPager data with new transactions and events.
     */
    private fun updateViewPagerData(transactions: List<SearchTransaction>, events: List<SearchEvent>) {
        pagerAdapter.updateData(transactions, events)
    }

    /**
     * Performs a search with the given query.
     *
     * @param query The search query string.
     */
    private fun performSearch(query: String) {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.search(token, query)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
        }
    }

    /**
     * Deletes a transaction by its short UUID.
     */
    private fun deleteTransaction(shortUuid: String) {
        val token = sessionManager.getAuthToken()
        val currentQuery = viewModel.searchQuery.value
        if (token != null && currentQuery != null) {
            viewModel.deleteTransaction(token, shortUuid, currentQuery)
        } else {
            Toast.makeText(requireContext(), "خطا در حذف تراکنش", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Deletes an event by its short UUID.
     */
    private fun deleteEvent(shortUuid: String) {
        val token = sessionManager.getAuthToken()
        val currentQuery = viewModel.searchQuery.value
        if (token != null && currentQuery != null) {
            viewModel.deleteEvent(token, shortUuid, currentQuery)
        } else {
            Toast.makeText(requireContext(), "خطا در حذف رویداد", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Navigates to the edit transaction screen.
     */
    private fun navigateToEditTransaction(shortUuid: String, transaction: SearchTransaction) {
        val bundle = Bundle().apply {
            putString("shortUuid", shortUuid)
            if (transaction.mode == "I") {
                putParcelable("incomeObject", convertToIncomeObject(transaction))
            } else {
                putParcelable("expenditureObject", convertToExpenditureObject(transaction))
            }
        }
        findNavController().navigate(R.id.action_searchResultsFragment_to_editTransactionFragment, bundle)
    }

    /**
     * Navigates to the edit event screen.
     */
    private fun navigateToEditEvent(shortUuid: String, event: SearchEvent) {
        val bundle = Bundle().apply {
            putString("shortUuid", shortUuid)
            putParcelable("eventObject", convertToEventObject(event))
        }
        findNavController().navigate(R.id.action_searchResultsFragment_to_editEventFragment, bundle)
    }

    /**
     * Converts SearchTransaction to IncomeObject for editing.
     */
    private fun convertToIncomeObject(transaction: SearchTransaction): IncomeObject {
        return IncomeObject(
            id = transaction.id,
            mode = transaction.mode,
            title = transaction.title,
            author = transaction.author,
            amount = transaction.amount,
            year = transaction.year,
            month = transaction.month,
            day = transaction.day,
            bankInfo = transaction.bankInfo,
            categoryInfo = transaction.categoryInfo,
            tags = null, // SearchTransaction doesn't have tag IDs, only names
            tagsNames = transaction.tagsNames,
            shortUuid = transaction.shortUuid,
            active = transaction.active,
            created = transaction.created,
            slashedDatePersian = transaction.slashedDatePersian,
            updated = transaction.updated
        )
    }

    /**
     * Converts SearchTransaction to ExpenditureObject for editing.
     */
    private fun convertToExpenditureObject(transaction: SearchTransaction): ExpenditureObject {
        return ExpenditureObject(
            id = transaction.id,
            mode = transaction.mode,
            title = transaction.title,
            author = transaction.author,
            amount = transaction.amount,
            year = transaction.year,
            month = transaction.month,
            day = transaction.day,
            bankInfo = transaction.bankInfo,
            categoryInfo = transaction.categoryInfo,
            tags = null, // SearchTransaction doesn't have tag IDs, only names
            tagsNames = transaction.tagsNames,
            shortUuid = transaction.shortUuid,
            active = transaction.active,
            created = transaction.created,
            slashedDatePersian = transaction.slashedDatePersian,
            updated = transaction.updated
        )
    }

    /**
     * Converts SearchEvent to EventObject for editing.
     */
    private fun convertToEventObject(event: SearchEvent): EventObject {
        return EventObject(
            id = event.id,
            title = event.title,
            author = event.author,
            year = event.year,
            month = event.month,
            day = event.day,
            slashedDate = event.slashedDate,
            slashedDatePersian = event.slashedDatePersian,
            numericalDate = event.numericalDate.toInt(),
            active = event.active,
            shortUuid = event.shortUuid,
            created = event.created,
            updated = event.updated
        )
    }



    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
