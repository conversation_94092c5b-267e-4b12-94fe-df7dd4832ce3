package local.bestoon.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.SearchTransaction
import java.text.NumberFormat
import java.util.Locale

/**
 * Adapter for displaying search transaction results using the existing income/expenditure layout.
 */
class SearchTransactionAdapter : RecyclerView.Adapter<SearchTransactionAdapter.SearchTransactionViewHolder>() {

    private var transactionList: List<SearchTransaction> = emptyList()

    class SearchTransactionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun getItemViewType(position: Int): Int {
        // Return different view types for income (I) and expenditure (E)
        return if (transactionList[position].mode == "I") 0 else 1
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchTransactionViewHolder {
        // Use income layout for income transactions, expenditure layout for expenditure transactions
        val layoutId = if (viewType == 0) R.layout.item_income else R.layout.item_expenditure
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return SearchTransactionViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchTransactionViewHolder, position: Int) {
        val transaction = transactionList[position]
        
        holder.textShortUuid.text = transaction.shortUuid
        holder.textBank.text = transaction.bankInfo?.title ?: "نامشخص"
        holder.textCreated.text = transaction.slashedDatePersian

        // Handle title - hide if empty or null
        if (!transaction.title.isNullOrEmpty()) {
            holder.textTitle.text = transaction.title
            holder.textTitle.visibility = View.VISIBLE
        } else {
            holder.textTitle.visibility = View.GONE
        }

        holder.textCategory.text = "دسته‌بندی: ${transaction.categoryInfo?.title ?: "نامشخص"}"

        // Handle tags - hide if empty
        if (transaction.tagsNames.isNotEmpty()) {
            holder.textTags.text = "برچسب‌ها: ${transaction.tagsNames.joinToString(", ")}"
            holder.textTags.visibility = View.VISIBLE
        } else {
            holder.textTags.visibility = View.GONE
        }

        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(transaction.amount)} تومان"

        // Hide menu button for search results (no edit/delete functionality)
        holder.btnMenu.visibility = View.GONE
    }

    override fun getItemCount(): Int = transactionList.size

    fun updateData(newTransactionList: List<SearchTransaction>) {
        transactionList = newTransactionList
        notifyDataSetChanged()
    }
}
