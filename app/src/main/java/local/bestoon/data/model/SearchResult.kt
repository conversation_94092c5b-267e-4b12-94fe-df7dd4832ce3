package local.bestoon.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing the search results from the API.
 *
 * @property matchedTickets List of tickets matching the search query.
 * @property queriedString The search query string that was used.
 */
data class SearchResult(
    @SerializedName("matched_tickets") val matchedTickets: List<Ticket>,
    @SerializedName("queried_string") val queriedString: String
)
