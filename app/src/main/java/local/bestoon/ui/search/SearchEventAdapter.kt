package local.bestoon.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.SearchEvent

/**
 * Adapter for displaying search event results using the existing event layout.
 */
class SearchEventAdapter : RecyclerView.Adapter<SearchEventAdapter.SearchEventViewHolder>() {

    private var eventList: List<SearchEvent> = emptyList()

    class SearchEventViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textDate: TextView = itemView.findViewById(R.id.text_date)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchEventViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event, parent, false)
        return SearchEventViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchEventViewHolder, position: Int) {
        val event = eventList[position]

        holder.textShortUuid.text = event.shortUuid
        holder.textTitle.text = event.title
        holder.textDate.text = event.slashedDatePersian

        // Hide menu button for search results (no edit/delete functionality)
        holder.btnMenu.visibility = View.GONE
    }

    override fun getItemCount(): Int = eventList.size

    fun updateData(newEventList: List<SearchEvent>) {
        eventList = newEventList
        notifyDataSetChanged()
    }
}
