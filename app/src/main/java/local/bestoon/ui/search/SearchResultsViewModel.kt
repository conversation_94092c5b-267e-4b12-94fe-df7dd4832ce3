package local.bestoon.ui.search

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import local.bestoon.data.model.SearchResult
import local.bestoon.data.model.SearchTransaction
import local.bestoon.data.model.SearchEvent
import local.bestoon.data.repository.SearchRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the search results screen.
 */
class SearchResultsViewModel : ViewModel() {

    private val searchRepository = SearchRepository()

    private val _searchResults = MutableLiveData<SearchResult>()
    val searchResults: LiveData<SearchResult> = _searchResults

    private val _transactions = MutableLiveData<List<SearchTransaction>>()
    val transactions: LiveData<List<SearchTransaction>> = _transactions

    private val _events = MutableLiveData<List<SearchEvent>>()
    val events: LiveData<List<SearchEvent>> = _events

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _searchQuery = MutableLiveData<String>()
    val searchQuery: LiveData<String> = _searchQuery

    /**
     * Performs a search with the given query.
     *
     * @param token The authentication token.
     * @param query The search query string.
     */
    fun search(token: String, query: String) {
        _isLoading.value = true
        _error.value = null
        _searchQuery.value = query

        viewModelScope.launch {
            val result = searchRepository.search(token, query)
            _isLoading.value = false

            result.fold(
                onSuccess = { searchResult ->
                    _searchResults.value = searchResult
                    _transactions.value = searchResult.matchedTransactions
                    _events.value = searchResult.matchedEvents
                },
                onFailure = { exception ->
                    _error.value = exception.message
                }
            )
        }
    }
}
