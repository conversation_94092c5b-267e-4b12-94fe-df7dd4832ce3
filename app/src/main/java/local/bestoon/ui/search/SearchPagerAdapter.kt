package local.bestoon.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.SearchTransaction
import local.bestoon.data.model.SearchEvent

/**
 * Adapter for the ViewPager2 in search results, handling tabs for transactions and events.
 */
class SearchPagerAdapter : RecyclerView.Adapter<SearchPagerAdapter.TabViewHolder>() {

    private var transactionList: List<SearchTransaction> = emptyList()
    private var eventList: List<SearchEvent> = emptyList()
    
    private var transactionAdapter: SearchTransactionAdapter? = null
    private var eventAdapter: SearchEventAdapter? = null

    sealed class TabViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        class TransactionViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_transactions)
        }

        class EventViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_events)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return position // 0 = Transactions, 1 = Events
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        return when (viewType) {
            0 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_search_transactions, parent, false)
                TabViewHolder.TransactionViewHolder(view)
            }
            1 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_search_events, parent, false)
                TabViewHolder.EventViewHolder(view)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        when (holder) {
            is TabViewHolder.TransactionViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                if (transactionAdapter == null) {
                    transactionAdapter = SearchTransactionAdapter()
                    holder.recyclerView.adapter = transactionAdapter
                } else {
                    if (holder.recyclerView.adapter == null) {
                        holder.recyclerView.adapter = transactionAdapter
                    }
                }
                transactionAdapter?.submitList(transactionList)
            }
            is TabViewHolder.EventViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                if (eventAdapter == null) {
                    eventAdapter = SearchEventAdapter()
                    holder.recyclerView.adapter = eventAdapter
                } else {
                    if (holder.recyclerView.adapter == null) {
                        holder.recyclerView.adapter = eventAdapter
                    }
                }
                eventAdapter?.submitList(eventList)
            }
        }
    }

    override fun getItemCount(): Int = 2 // Transactions, Events

    fun updateData(
        newTransactionList: List<SearchTransaction>,
        newEventList: List<SearchEvent>
    ) {
        transactionList = newTransactionList
        eventList = newEventList

        // Update existing adapters if they exist
        transactionAdapter?.submitList(newTransactionList)
        eventAdapter?.submitList(newEventList)

        notifyDataSetChanged()
    }
}
