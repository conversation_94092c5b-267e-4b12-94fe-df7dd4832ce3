package local.bestoon.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.SearchEvent

/**
 * Adapter for displaying search event results in a RecyclerView.
 */
class SearchEventAdapter : ListAdapter<SearchEvent, SearchEventAdapter.SearchEventViewHolder>(SearchEventDiffCallback()) {

    class SearchEventViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAuthor: TextView = itemView.findViewById(R.id.text_author)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchEventViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_search_event, parent, false)
        return SearchEventViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchEventViewHolder, position: Int) {
        val event = getItem(position)
        
        holder.textShortUuid.text = event.shortUuid
        holder.textTitle.text = event.title
        holder.textAuthor.text = event.author?.username ?: "نامشخص"
        holder.textCreated.text = event.slashedDatePersian
    }

    class SearchEventDiffCallback : DiffUtil.ItemCallback<SearchEvent>() {
        override fun areItemsTheSame(oldItem: SearchEvent, newItem: SearchEvent): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: SearchEvent, newItem: SearchEvent): Boolean {
            return oldItem == newItem
        }
    }
}
